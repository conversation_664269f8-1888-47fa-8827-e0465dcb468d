import 'dart:async';
import 'dart:ui';

import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/voip/voip_cubit.dart';
import 'package:ddone/init.dart';
import 'package:ddone/models/enums/voip_sip_event_enum.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/audio_session_service.dart';
import 'package:ddone/services/callkit_service.dart';
import 'package:ddone/services/janus_service.dart';
import 'package:ddone/services/notification_service.dart';
import 'package:ddone/services/shared_preferences_service.dart';
import 'package:ddone/utils/async_utils.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:ddone/utils/string_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:janus_client/janus_client.dart';

enum ForegroundMethod {
  boardcastEvent,
  retrieveState,
  retrieveMainMasterId,
  receiveMainMasterId,
  retrieveForegroundMasterId,
  receiveForegroundMasterId,
  retrieveForegroundIsReady,
  receiveForegroundIsReady,
  stop,
  informStop,
  acceptCall,
  declineCall,
  makeCall,
  holdCall,
  unholdCall,
  toggleHoldCall,
  hangupCall,
  muteMicAudio,
  muteSpeakerAudio,
}

class ForegroundService {
  // static const String _notificationChannelId = 'voip_foreground_channel';

  static bool _onStartHasRun = false;
  static bool _listenerReady = false;
  static bool _janusReady = false;
  static bool _acceptingCall = false;
  static String? _foregroundMasterId;
  static String? _mainMasterId;
  static VoipState? lastVoipState;
  static ServiceInstance? _serviceInstance;

  // initialize in onStart's _initDependencies
  static late JanusService _janusService;
  static late CallkitService _callkitService;
  static late AudioSessionService _audioSessionService;
  static late SharedPreferencesService _prefs;

  // This class should not be instantiated. It is a static controller class.
  ForegroundService._();

  /// Initialize the foreground service
  static Future<void> initialize() async {
    try {
      final service = FlutterBackgroundService();

      await service.configure(
        iosConfiguration: IosConfiguration(
          autoStart: false,
          onForeground: onStart,
          onBackground: onIosBackground,
        ),
        androidConfiguration: AndroidConfiguration(
          onStart: onStart,
          autoStart: false,
          isForegroundMode: true,
          notificationChannelId: androidNotificationHighChannelId,
          initialNotificationTitle: 'DDOne Call',
          initialNotificationContent: 'Setting up connection...',
          foregroundServiceNotificationId: 888,
          foregroundServiceTypes: [AndroidForegroundType.phoneCall],
        ),
      );
      log.t('Successfully initialized ForegroundService.');
    } catch (e) {
      log.e('Failed to initialize ForegroundService.', error: e);
    }
  }

  @pragma('vm:entry-point')
  static void onStart(ServiceInstance service) async {
    _serviceInstance = service;

    // We don't need to do this, i think this is only for case where we use flutter_background_service
    // as auto start background service. For manually triggered foreground service, it will hit exception. See:
    // https://github.com/ekasetiawans/flutter_background_service/issues/375#issuecomment-2796041087
    // DartPluginRegistrant.ensureInitialized();

    if (_onStartHasRun) return; // prevent double start
    _onStartHasRun = true;

    // Listen for methods.
    service.on(ForegroundMethod.retrieveState.name).listen(_retrieveState);
    service.on(ForegroundMethod.retrieveForegroundMasterId.name).listen(_retrieveForegroundMasterId);
    service.on(ForegroundMethod.receiveMainMasterId.name).listen(_receiveMainMasterId);
    service.on(ForegroundMethod.retrieveForegroundIsReady.name).listen(_retrieveforegroundIsReady);
    service.on(ForegroundMethod.stop.name).listen(_stop);
    service.on(ForegroundMethod.acceptCall.name).listen(_acceptCall);
    service.on(ForegroundMethod.declineCall.name).listen(_declineCall);
    service.on(ForegroundMethod.makeCall.name).listen(_makeCall);
    service.on(ForegroundMethod.holdCall.name).listen(_holdCall);
    service.on(ForegroundMethod.unholdCall.name).listen(_unholdCall);
    service.on(ForegroundMethod.toggleHoldCall.name).listen(_toggleHoldCall);
    service.on(ForegroundMethod.hangupCall.name).listen(_hangupCall);
    service.on(ForegroundMethod.muteMicAudio.name).listen(_muteMicAudio);
    service.on(ForegroundMethod.muteSpeakerAudio.name).listen(_muteSpeakerAudio);
    _listenerReady = true;

    await _initDependencies();

    await _setupJanus();

    // Update notification periodically to keep service alive
    Timer.periodic(const Duration(seconds: 1), (timer) async {
      if (service is AndroidServiceInstance) {
        if (await service.isForegroundService()) {
          String content = 'Call in progress...';
          int? callkitStartCallTime = _prefs.getInt(CacheKeys.callkitStartCallTime);
          if (callkitStartCallTime != null) {
            int currentTimestamp = DateTime.now().millisecondsSinceEpoch;
            int callDurationInSeconds = (currentTimestamp - callkitStartCallTime) ~/ 1000;
            content = 'Call in progress - ${_formatDuration(callDurationInSeconds)}';
          }
          service.setForegroundNotificationInfo(
            title: 'DDOne Call',
            content: content,
          );
        }
      }
    });
  }

  static String _formatDuration(int totalSeconds) {
    Duration duration = Duration(seconds: totalSeconds);
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    // If you need hours, consider if you want to display them even if 0
    String formattedHours = twoDigits(duration.inHours);
    // Option 1: Display hours only if greater than 0, or always if you prefer
    if (duration.inHours > 0) {
      return '$formattedHours:$twoDigitMinutes:$twoDigitSeconds';
    } else {
      // For durations less than an hour, show 00:mm:ss or just mm:ss
      // Depending on your exact requirement, you might want to show "00:mm:ss"
      return '00:$twoDigitMinutes:$twoDigitSeconds'; // or simply '$twoDigitMinutes:$twoDigitSeconds'
    }
  }

  static Future<void> _initDependencies() async {
    await Future.wait([
      mainInit(),
      initializeLogger(),
    ]);
    _janusService = sl.get<JanusService>();
    _callkitService = sl.get<CallkitService>();
    _audioSessionService = sl.get<AudioSessionService>();
    _prefs = sl.get<SharedPreferencesService>();
  }

  static Future<void> _setupJanus() async {
    try {
      log.t('Init janus in foreground service...');
      await _prefs.reload();
      String sipWsUrl = _prefs.getString(CacheKeys.sipWsUrl)!;
      await _janusService.init(sipWsUrl);
      log.t('Init janus in foreground service success.');
    } catch (e) {
      log.e('Failed to init janus in foreground service.', error: e);
      return;
    }

    try {
      _janusService.mediaOccupiedCallback('callkit_ringing', () async {
        return _callkitService.isRinging;
      });

      await _janusService.initMedia();

      _janusService.addJanusMessageListener(
        kForegroundServiceJanusEventPriority,
        _onJanusEvent,
        onError: _onJanusError,
      );

      log.t('Register janus in foreground service...');
      String? token = _prefs.getString(CacheKeys.sipHeaderToken);
      String? sipNumber = _prefs.getString(CacheKeys.sipNumber);
      String? sipDomain = _prefs.getString(CacheKeys.sipDomain);
      String? sipProxy = _prefs.getString(CacheKeys.sipProxy);
      String? sipSecret = _prefs.getString(CacheKeys.sipPwd);
      String? sipName = _prefs.getString(CacheKeys.sipName);
      await _janusService.register('sip:$sipNumber@$sipDomain', 'sip:$sipProxy', sipSecret!, sipName, token);
      log.t('Register janus in foreground service success.');
    } catch (e) {
      log.e('Failed to register janus in foreground service.', error: e);
    }
  }

  static Future<void> _disposeJanus() async {
    log.t('Dispose janus in foreground service...');
    _foregroundMasterId = null;
    _janusReady = false;
    await _janusService.dispose();
    _janusService.removeJanusMessageListener(kForegroundServiceJanusEventPriority, _onJanusEvent,
        onError: _onJanusError);
    log.t('Dispose janus in foreground completed');
  }

  static void _onJanusEvent(TypedEvent<JanusEvent> event) async {
    Object data = event.event.plugindata?.data;
    log.t('foreground service janus listener data: $data');
    if (data is SipRegisteredEvent) {
      if (data.result?.masterId != null) _foregroundMasterId = '${data.result!.masterId!}';
      _janusReady = true;
    } else if (data is SipIncomingCallEvent) {
      _prefs.remove(CacheKeys.callkitStartCallTime);
    } else if (data is SipAcceptedEvent) {
      _prefs.setInt(CacheKeys.callkitStartCallTime, DateTime.now().millisecondsSinceEpoch);
      _boardcastEvent(VoipSipAcceptedSuccess(
          statusMessage: VoipSipEvent.accepted.statusMessage(),
          caller: lastVoipState?.caller,
          callerId: lastVoipState?.callerId,
          callee: lastVoipState?.callee,
          calleeId: lastVoipState?.callee));
    } else if (data is SipProgressEvent) {
      _boardcastEvent(VoipSipProgress(
          statusMessage: VoipSipEvent.progress.statusMessage(),
          caller: lastVoipState?.caller,
          callerId: lastVoipState?.callerId,
          callee: lastVoipState?.callee,
          calleeId: lastVoipState?.callee));
    } else if (data is SipCallingEvent) {
      _prefs.remove(CacheKeys.callkitStartCallTime);
      Map<String, String> callInfo = await _callkitService.getCallerInfo();
      _boardcastEvent(VoipSipCalling(
          statusMessage: VoipSipEvent.calling.statusMessage(),
          calleeId: callInfo['callerId'],
          callee: callInfo['caller']));
    } else if (data is SipProceedingEvent) {
      _boardcastEvent(VoipSipProceeding(
          statusMessage: VoipSipEvent.proceeding.statusMessage(),
          caller: lastVoipState?.caller,
          callerId: lastVoipState?.callerId,
          callee: lastVoipState?.callee,
          calleeId: lastVoipState?.callee));
    } else if (data is SipRingingEvent) {
      _boardcastEvent(VoipSipRinging(
          caller: lastVoipState?.caller,
          callerId: lastVoipState?.callerId,
          callee: lastVoipState?.callee,
          calleeId: lastVoipState?.callee));
    } else if (data is SipHangupEvent) {
      _callkitService.endCall();
      _prefs.remove(CacheKeys.callkitStartCallTime);

      // Wait a bit before dispose. Sometimes there are something still need to be
      // written to websocket or something duno what after hang up. Let it finish before dispose.
      await Future.delayed(const Duration(milliseconds: 500));
      await _disposeJanus();
      _audioSessionService.resetSession();
      _boardcastEvent(VoipSipHungUp(statusMessage: VoipSipEvent.hangup.statusMessage()));
      _stop(null);
    } else if (data is SipUnRegisteredEvent) {
      _foregroundMasterId = null;
    } else if (data is SipTransferCallEvent) {
      _boardcastEvent(const VoipSipTransferCall());
    } else if (data is SipMissedCallEvent) {}
    log.t('foreground service janus listener completed');
  }

  static void _onJanusError(dynamic error) async {
    log.e('foreground service janus listener error', error: error);
    if (error is JanusError) {
      _stop(null);
    }
  }

  static void _boardcastEvent(VoipState voipState) {
    _serviceInstance!.invoke(
      ForegroundMethod.boardcastEvent.name,
      {
        'event': voipState.toJson(),
      },
    );
    lastVoipState = voipState;
  }

  static void _retrieveforegroundIsReady(Map<String, dynamic>? event) async {
    bool waitResult = await waitForCondition(() async => _janusReady, timeout: const Duration(milliseconds: 5000));
    log.d('foregroundIsReady: $waitResult');
    _serviceInstance!.invoke(ForegroundMethod.receiveForegroundIsReady.name, {'isReady': _janusReady});
  }

  static void _stop(Map<String, dynamic>? event) async {
    log.t('Foreground service stopping...');
    _serviceInstance!.invoke(ForegroundMethod.informStop.name);
    await _disposeJanus();
    _serviceInstance!.stopSelf();
    _serviceInstance = null;
    lastVoipState = null;
    log.t('Foreground service stopped');
  }

  static void _retrieveState(Map<String, dynamic>? event) async {
    log.t('Foreground service retrieve state');
    if (lastVoipState != null) _boardcastEvent(lastVoipState!);
  }

  static void _retrieveForegroundMasterId(Map<String, dynamic>? event) async {
    log.t('Foreground service retrieve foreground master id');
    _serviceInstance!.invoke(ForegroundMethod.receiveForegroundMasterId.name, {'masterId': _foregroundMasterId});
  }

  static void _receiveMainMasterId(Map<String, dynamic>? event) async {
    log.t('Foreground service receive main master id. ${StringUtil.prettyPrint(event)}');
    _mainMasterId = event!['masterId'];
  }

  static void _acceptCall(Map<String, dynamic>? event) async {
    if (_acceptingCall) {
      log.t('Foreground service already accepting call. Skipped.');
      return;
    }
    try {
      _acceptingCall = true;
      log.t('Accepting call in foreground service...');
      Map<String, String> callInfo = await _callkitService.getCallerInfo();
      _boardcastEvent(VoipSipAcceptedLoading(
          statusMessage: VoipSipEvent.loading.statusMessage(),
          caller: callInfo['caller'],
          callerId: callInfo['callerId']));
      // only android need this, iOS audio session is handled by callkit, windows no need care.
      await _audioSessionService.activateCallSession();
      await _janusService.initLocalStream();
      await _janusService.acceptCall();
      log.t('Accepted call in foreground service.');
    } catch (e) {
      log.e('Failed to accept call in foreground service.', error: e);
      _boardcastEvent(VoipSipAcceptedError(statusMessage: e.toString()));
      _stop(null);
    } finally {
      _acceptingCall = false;
    }
  }

  static void _declineCall(Map<String, dynamic>? event) async {
    try {
      log.t('Declining call in foreground service...');
      _boardcastEvent(VoipSipHangingUp(statusMessage: VoipSipEvent.hangingup.statusMessage()));
      await _janusService.declineCall();
      log.t('Declined call in foreground service.');
    } catch (e) {
      log.e('Failed to decline call in foreground service.', error: e);
      _callkitService.endCall();
      _stop(null);
    }
  }

  static void _makeCall(Map<String, dynamic>? event) async {
    try {
      log.t('Making call in foreground service...');
      String extNum = event!['extNum'];
      String sipDomain = event['sipDomain'];
      String ctcName = event['ctcName'];
      // only android need this, iOS audio session is handled by callkit, windows no need care.
      await _audioSessionService.activateCallSession();
      await _janusService.initLocalStream();
      _janusService.makeCall(extNum, sipDomain);
      _boardcastEvent(
          VoipSipCalling(statusMessage: VoipSipEvent.calling.statusMessage(), calleeId: extNum, callee: ctcName));
    } catch (e) {
      log.e('Failed to make call in foreground service.', error: e);
    }
  }

  static void _holdCall(Map<String, dynamic>? event) async {
    try {
      log.t('holdCall in foreground service...');
      await _janusService.holdCall();
    } catch (e) {
      log.e('Failed to holdCall in foreground service.', error: e);
    }
  }

  static void _unholdCall(Map<String, dynamic>? event) async {
    try {
      log.t('unholdCall in foreground service...');
      await _janusService.unholdCall();
    } catch (e) {
      log.e('Failed to unholdCall in foreground service.', error: e);
    }
  }

  static void _toggleHoldCall(Map<String, dynamic>? event) async {
    try {
      log.t('toggleHoldCall in foreground service...');
      await _janusService.toggleHoldCall();
    } catch (e) {
      log.e('Failed to toggleHoldCall in foreground service.', error: e);
    }
  }

  static void _hangupCall(Map<String, dynamic>? event) async {
    try {
      log.t('hangupCall in foreground service...');
      _boardcastEvent(VoipSipHangingUp(statusMessage: VoipSipEvent.hangingup.statusMessage()));
      await _janusService.hangupCall();
    } catch (e) {
      log.e('Failed to hangupCall in foreground service.', error: e);
    }
  }

  static void _muteMicAudio(Map<String, dynamic>? event) async {
    try {
      log.t('muteMicAudio in foreground service...');
      bool mute = event!['mute'];
      await _janusService.muteMicAudio(mute);
    } catch (e) {
      log.e('Failed to muteMicAudio in foreground service.', error: e);
    }
  }

  static void _muteSpeakerAudio(Map<String, dynamic>? event) async {
    try {
      log.t('muteSpeakerAudio in foreground service...');
      bool mute = event!['mute'];
      await _janusService.muteSpeakerAudio(mute);
    } catch (e) {
      log.e('Failed to muteSpeakerAudio in foreground service.', error: e);
    }
  }

  static Future<String?> getMainMasterId() async {
    _serviceInstance!.invoke(ForegroundMethod.retrieveMainMasterId.name);
    bool waitResult =
        await waitForCondition(() async => _mainMasterId != null, timeout: const Duration(milliseconds: 5000));
    log.d('getMainMasterId: $waitResult, _mainMasterId: $_mainMasterId');
    return _mainMasterId;
  }

  /// iOS background handler
  /// Just a placeholder, doesn't do anything.
  @pragma('vm:entry-point')
  static Future<bool> onIosBackground(ServiceInstance service) async {
    WidgetsFlutterBinding.ensureInitialized();
    DartPluginRegistrant.ensureInitialized();
    return true;
  }

  static Future<void> start() async {
    log.t('Starting foreground service...');
    final service = FlutterBackgroundService();
    if (await service.isRunning()) {
      log.t('Foreground service already running.');
      return;
    }
    await service.startService();
    log.t('Completed starting foreground service.');
  }

  static Future<bool> isRunning() async {
    return isAndroid && await FlutterBackgroundService().isRunning();
  }

  /// Invoke method in foreground service
  static Future<void> invokeMethod(ForegroundMethod method, [Map<String, dynamic>? arg]) async {
    final service = FlutterBackgroundService();
    if (!await service.isRunning()) {
      log.t('Foreground service not running. Skipped "${method.name}".');
      return;
    }
    try {
      log.t('Invoking method "${method.name}" in foreground service...');
      await waitForCondition(() async => _listenerReady, timeout: const Duration(milliseconds: 5000));
      service.invoke(method.name, arg);
      log.t('invoked "${method.name}".');
    } catch (e) {
      log.e('Failed to invoke "${method.name}".', error: e);
    }
  }
}
